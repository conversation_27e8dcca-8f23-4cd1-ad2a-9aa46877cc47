# Ignore specified folders
iterations/
venv/
.langgraph_api/
.github/
__pycache__/
.env

# Git related
.git/
.gitignore
.gitattributes

# Python cache
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Keep the example env file for reference
!.env.example
