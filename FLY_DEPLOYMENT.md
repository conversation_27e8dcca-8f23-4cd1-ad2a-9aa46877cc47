# Deploying Archon to Fly.io

This guide walks you through deploying Archon's microservices architecture to Fly.io. Archon consists of multiple services that work together to provide a complete AI coding assistant platform.

## 🏗️ Architecture Overview

Archon consists of four main services:

1. **Archon Server** (`archon-server`) - Core API, web crawling, document processing
2. **MCP Server** (`archon-mcp`) - Model Context Protocol interface for AI clients
3. **Agents Service** (`archon-agents`) - AI/ML operations and reranking
4. **Frontend** (`archon-frontend`) - React web interface

## 📋 Prerequisites

Before deploying to Fly.io, ensure you have:

- [Fly.io CLI](https://fly.io/docs/hands-on/install-flyctl/) installed and authenticated
- [Supabase](https://supabase.com/) account with a project set up
- [OpenAI API key](https://platform.openai.com/api-keys) (or alternative AI provider)
- Docker installed locally (for building images)

## 🚀 Quick Start Deployment

### 1. Prepare Your Environment

```bash
# Clone the repository
git clone https://github.com/coleam00/archon.git
cd archon

# Copy environment template
cp .env.example .env
# Edit .env with your configuration (for local testing)
```

### 2. Set Up Supabase Database

In your Supabase project's SQL Editor, run:

```sql
-- Copy and paste the contents of migration/complete_setup.sql
-- This creates all necessary tables and functions
```

### 3. Deploy Services in Order

Deploy services in this specific order due to dependencies:

#### Step 3a: Deploy Archon Server (Core Service)

```bash
# Create and deploy the server app
fly apps create archon-server
fly deploy -c fly.toml

# Set required secrets
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-server
fly secrets set SUPABASE_SERVICE_KEY="your-service-key" -a archon-server
fly secrets set OPENAI_API_KEY="sk-your-openai-key" -a archon-server

# Optional: Set Logfire token if using advanced logging
fly secrets set LOGFIRE_TOKEN="your-logfire-token" -a archon-server
```

#### Step 3b: Deploy Agents Service

```bash
# Create and deploy the agents app
fly apps create archon-agents
fly deploy -c fly-agents.toml

# Set required secrets
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-agents
fly secrets set SUPABASE_SERVICE_KEY="your-service-key" -a archon-agents
fly secrets set OPENAI_API_KEY="sk-your-openai-key" -a archon-agents
```

#### Step 3c: Deploy MCP Server

```bash
# Create and deploy the MCP app
fly apps create archon-mcp

# Update fly-mcp.toml with your actual service URLs
# Edit the API_SERVICE_URL and AGENTS_SERVICE_URL in fly-mcp.toml

fly deploy -c fly-mcp.toml

# Set required secrets
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-mcp
fly secrets set SUPABASE_SERVICE_KEY="your-service-key" -a archon-mcp
```

#### Step 3d: Deploy Frontend

```bash
# Create and deploy the frontend app
fly apps create archon-frontend

# Update fly-frontend.toml with your server URL
# Edit VITE_API_URL to point to your archon-server app

fly deploy -c fly-frontend.toml
```

### 4. Configure API Keys in the UI

1. Open your frontend URL: `https://archon-frontend.fly.dev`
2. Go to **Settings** → Select your LLM/embedding provider
3. Set your API key (this will be stored in Supabase)
4. Test by uploading a document or crawling a website

## 🔧 Configuration Details

### Environment Variables

Each service uses different environment variables. Key ones include:

- `SERVICE_DISCOVERY_MODE=fly_io` - Enables Fly.io service discovery
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_SERVICE_KEY` - Your Supabase service role key
- `OPENAI_API_KEY` - OpenAI API key for embeddings and AI operations

### Service Communication

Services communicate via HTTPS using Fly.io's internal networking:

- MCP Server → Archon Server: `https://archon-server.fly.dev`
- MCP Server → Agents Service: `https://archon-agents.fly.dev`
- Frontend → Archon Server: `https://archon-server.fly.dev`

### Resource Allocation

Default resource allocation per service:

- **Archon Server**: 2GB RAM, 2 CPUs (handles web crawling and document processing)
- **Agents Service**: 4GB RAM, 2 CPUs (AI operations require more memory)
- **MCP Server**: 1GB RAM, 1 CPU (lightweight HTTP wrapper)
- **Frontend**: 1GB RAM, 1 CPU (static React app)

## 🔍 Monitoring and Health Checks

### Health Check Endpoints

- **Server**: `https://archon-server.fly.dev/health`
- **Agents**: `https://archon-agents.fly.dev/health`
- **MCP**: `https://archon-mcp.fly.dev/sse`
- **Frontend**: `https://archon-frontend.fly.dev/`

### Viewing Logs

```bash
# View logs for each service
fly logs -a archon-server
fly logs -a archon-mcp
fly logs -a archon-agents
fly logs -a archon-frontend

# Follow logs in real-time
fly logs -f -a archon-server
```

### Scaling Services

```bash
# Scale services based on load
fly scale count 2 -a archon-server    # Scale server for more requests
fly scale count 3 -a archon-agents    # Scale agents for more AI operations
fly scale memory 8gb -a archon-agents # Increase memory for AI operations
```

## 🔒 Security Considerations

### Secrets Management

Never commit sensitive values to your repository. Use Fly.io secrets:

```bash
# Set secrets securely
fly secrets set SUPABASE_SERVICE_KEY="your-key" -a archon-server
fly secrets list -a archon-server  # View set secrets (values hidden)
```

### Network Security

- All services use HTTPS by default
- Supabase handles database security with Row Level Security (RLS)
- API keys are stored encrypted in Supabase

## 🛠️ Troubleshooting

### Common Issues

1. **Services can't communicate**: Update service URLs in fly.toml files
2. **Database connection fails**: Check Supabase URL and service key
3. **Frontend can't reach API**: Update VITE_API_URL in fly-frontend.toml
4. **MCP connection issues**: Ensure MCP server is accessible and service URLs are correct

### Debug Commands

```bash
# Check app status
fly status -a archon-server

# SSH into a running machine
fly ssh console -a archon-server

# View machine details
fly machine list -a archon-server

# Restart a service
fly machine restart -a archon-server
```

## 🔄 Updates and Maintenance

### Deploying Updates

```bash
# Deploy updates to a specific service
fly deploy -c fly.toml -a archon-server

# Deploy with no-cache to force rebuild
fly deploy --no-cache -c fly.toml -a archon-server
```

### Database Migrations

When updating Archon, check for database migrations:

```sql
-- Run any new migration scripts in Supabase SQL Editor
-- Check migration/ directory for new .sql files
```

## 💡 Advanced Configuration

### Custom Domains

To use custom domains with your Archon deployment:

```bash
# Add a custom domain
fly certs create your-domain.com -a archon-frontend
fly certs create api.your-domain.com -a archon-server
fly certs create mcp.your-domain.com -a archon-mcp
```

### Environment-Specific Deployments

Create separate apps for staging and production:

```bash
# Create staging environment
fly apps create archon-server-staging
fly deploy -c fly.toml -a archon-server-staging

# Create production environment
fly apps create archon-server-prod
fly deploy -c fly.toml -a archon-server-prod
```

### Volume Storage

If you need persistent file storage:

```bash
# Create a volume
fly volumes create archon_data --size 10 -a archon-server

# Uncomment the [[mounts]] section in fly.toml
```

## 📚 Next Steps

After successful deployment:

1. **Test Web Crawling**: Crawl a documentation site
2. **Upload Documents**: Test PDF/document processing
3. **Create Projects**: Set up project and task management
4. **Connect AI Clients**: Use MCP server with Claude Code, Cursor, etc.
5. **Monitor Performance**: Watch logs and resource usage

## 🆘 Getting Help

- [Archon GitHub Discussions](https://github.com/coleam00/Archon/discussions)
- [Fly.io Documentation](https://fly.io/docs/)
- [Supabase Documentation](https://supabase.com/docs)

Your Archon deployment should now be running on Fly.io! 🎉
