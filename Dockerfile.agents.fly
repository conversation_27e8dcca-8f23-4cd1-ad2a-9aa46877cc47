# Agents Service - Lightweight PydanticAI agents ONLY (no ML models)
# Fly.io version - builds from root context

FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY python/requirements.agents.txt .
RUN pip install --no-cache-dir -r requirements.agents.txt

# Copy ONLY agents code - no dependencies on server code
# Agents use MCP tools for all operations
COPY python/src/agents/ src/agents/
COPY python/src/__init__.py src/

# Set environment variables
ENV PYTHONPATH="/app:$PYTHONPATH"
ENV PYTHONUNBUFFERED=1

# NO ML models in agents container!
# Agents use MCP tools for all ML operations

# Expose Agents port
ARG ARCHON_AGENTS_PORT=8052
ENV ARCHON_AGENTS_PORT=${ARCHON_AGENTS_PORT}
EXPOSE ${ARCHON_AGENTS_PORT}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD sh -c "python -c \"import urllib.request; urllib.request.urlopen('http://localhost:${ARCHON_AGENTS_PORT}/health')\""

# Run the Agents service
CMD sh -c "python -m uvicorn src.agents.server:app --host 0.0.0.0 --port ${ARCHON_AGENTS_PORT}"
