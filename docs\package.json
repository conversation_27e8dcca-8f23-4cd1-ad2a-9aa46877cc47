{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^3.8.0", "@docusaurus/preset-classic": "^3.8.0", "@docusaurus/theme-mermaid": "^3.8.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "lucide-react": "^0.513.0", "prism-react-renderer": "^2.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@xyflow/react": "^12.6.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}