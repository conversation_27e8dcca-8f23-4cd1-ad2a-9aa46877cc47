# Fly.io configuration for Archon Frontend (React UI)
# This deploys the React frontend application

app = "archon-frontend"
primary_region = "iad"  # Change to your preferred region
kill_signal = "SIGINT"
kill_timeout = "5s"

[experimental]
  auto_rollback = true

[build]
  dockerfile = "Dockerfile.frontend.fly"

[env]
  # Frontend Configuration
  NODE_ENV = "production"

  # API Configuration (update with your actual Fly.io server URL)
  VITE_API_URL = "https://archon-server.fly.dev"
  ARCHON_SERVER_PORT = "443"
  ARCHON_MCP_PORT = "8051"
  HOST = "archon-server.fly.dev"

  # Fly.io specific environment variables
  FLY_DEPLOYMENT = "true"

[http_service]
  internal_port = 5173
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800

[machine]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

[[services]]
  protocol = "tcp"
  internal_port = 5173
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["http", "tls"]

  [services.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800



[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

# Process configuration
[processes]
  app = "npm run dev -- --host 0.0.0.0"

# Deploy configuration
[deploy]
  release_command = "echo 'Archon Frontend starting...'"
  strategy = "rolling"

# Static file serving configuration
[[statics]]
  guest_path = "/app/dist"
  url_prefix = "/assets/"
