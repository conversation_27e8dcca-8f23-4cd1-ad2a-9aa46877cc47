# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.venv/
env/
venv/
ENV/
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
*.egg-info/
*.egg
dist/
build/
pip-log.txt
pip-delete-this-directory.txt

# Development
.git/
.gitignore
.github/
docs/
# tests/  # Keep tests for now as Dockerfile needs them
*.md
.env
.env.*
.editorconfig
.pre-commit-config.yaml
pytest.ini

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs and databases
*.log
logs/
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# Archives
*.zip
*.tar.gz
*.tgz
*.rar

# Old or backup files
old_rag/
backup/
*.old
*.backup

# Local development
.local/
.cache/
uploads/
downloads/

# Documentation build artifacts
_build/
site/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# UV/pip
uv.lock.bak