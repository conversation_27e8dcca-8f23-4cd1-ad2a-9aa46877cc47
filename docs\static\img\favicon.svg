<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 62 62" width="32" height="32">
  <defs>
    <style>
      .neon-path {
        fill: none;
        stroke: url(#neon-gradient);
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 2.5px;
        filter: drop-shadow(0 0 3px #00d38a);
      }
    </style>
    <linearGradient id="neon-gradient" x1=".53" y1="31.13" x2="61.72" y2="31.13" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00d38a"/>
      <stop offset=".08" stop-color="#0fcaa6"/>
      <stop offset=".25" stop-color="#36b5ef"/>
      <stop offset=".28" stop-color="#3fb1ff"/>
      <stop offset=".39" stop-color="#fe6aff"/>
      <stop offset=".42" stop-color="#d964ff"/>
      <stop offset=".48" stop-color="#ab5dff"/>
      <stop offset=".53" stop-color="#8a59ff"/>
      <stop offset=".57" stop-color="#7656ff"/>
      <stop offset=".6" stop-color="#6f55ff"/>
      <stop offset=".67" stop-color="#9a3df8"/>
      <stop offset=".75" stop-color="#c624f2"/>
      <stop offset=".81" stop-color="#e214ee"/>
      <stop offset=".85" stop-color="#ed0fed"/>
    </linearGradient>
  </defs>
  <path class="neon-path" d="M60.22,60.22s-2.71-4.5-6.53-11.26l-8.65-5.19h5.74c-7.01-12.65-15.94-29.93-19.66-41.75-1.71,5.18-3.72,10.25-6.05,15.18l4.78,6.33-6.88-1.87C13.95,40.44,2.03,60.22,2.03,60.22c6.84-4.68,14.39-8.24,22.37-10.52-.58-1.48-.87-3.06-.86-4.66,0-5.59,3.39-10.12,7.59-10.12s7.59,4.53,7.59,10.12c.01,1.59-.28,3.17-.86,4.66,7.97,2.29,15.52,5.84,22.37,10.52Z"/>
</svg> 