# Fly.io configuration for Archon MCP Server
# This deploys the MCP (Model Context Protocol) server for AI client integration

app = "archon-mcp"
primary_region = "iad"  # Change to your preferred region
kill_signal = "SIGINT"
kill_timeout = "5s"

[experimental]
  auto_rollback = true

[build]
  dockerfile = "Dockerfile.mcp.fly"

[env]
  # Service Configuration
  SERVICE_DISCOVERY_MODE = "fly_io"
  LOG_LEVEL = "INFO"
  PYTHONUNBUFFERED = "1"
  TRANSPORT = "sse"
  
  # Port Configuration
  ARCHON_MCP_PORT = "8051"
  ARCHON_SERVER_PORT = "8181"
  ARCHON_AGENTS_PORT = "8052"
  
  # Service URLs (update with your actual Fly.io app URLs)
  API_SERVICE_URL = "https://archon-server.fly.dev"
  AGENTS_SERVICE_URL = "https://archon-agents.fly.dev"

[http_service]
  internal_port = 8051
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 500
    soft_limit = 400

[machine]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

[[services]]
  protocol = "tcp"
  internal_port = 8051
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["http", "tls"]

  [services.concurrency]
    type = "connections"
    hard_limit = 500
    soft_limit = 400



[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

# Process configuration
[processes]
  app = "python -m src.mcp_server.mcp_server"

# Deploy configuration
[deploy]
  release_command = "echo 'Archon MCP Server starting...'"
  strategy = "rolling"

# Required secrets (set using `fly secrets set`):
# - SUPABASE_URL
# - SUPABASE_SERVICE_KEY
# - OPENAI_API_KEY (optional, for MCP tools that need AI)
# - LOGFIRE_TOKEN (optional)
