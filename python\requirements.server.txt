# Server Service Dependencies
# Web framework
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.20
watchfiles>=0.18  # For better hot reload performance

# Web crawling
crawl4ai==0.6.2

# Real-time communication
python-socketio[asyncio]>=5.11.0

# Database and storage
supabase==2.15.1
asyncpg>=0.29.0

# AI/ML libraries (ALL ML models belong here)
openai==1.71.0
# sentence-transformers>=4.1.0  # For reranking and advanced embeddings
# torch>=2.0.0  # Required by sentence-transformers
# transformers>=4.30.0  # Required by sentence-transformers

# Document processing
pypdf2>=3.0.1
pdfplumber>=0.11.6
python-docx>=1.1.2
markdown>=3.8

# Security and utilities
python-jose[cryptography]>=3.3.0
cryptography>=41.0.0
slowapi>=0.1.9

# Core utilities
httpx>=0.24.0
pydantic>=2.0.0
python-dotenv>=1.0.0
docker>=6.1.0  # For MCP container control

# Logging
logfire>=0.30.0

# Testing (needed for UI-triggered tests)
pytest>=8.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0