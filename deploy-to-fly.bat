@echo off
REM Archon Fly.io Deployment Script for Windows
REM This script helps deploy all Archon services to Fly.io in the correct order

setlocal enabledelayedexpansion

echo [INFO] Starting Archon deployment to Fly.io...

REM Check if flyctl is installed
fly version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Fly.io CLI is not installed. Please install it first:
    echo   https://fly.io/docs/hands-on/install-flyctl/
    exit /b 1
)
echo [SUCCESS] Fly.io CLI found

REM Check if user is logged in
fly auth whoami >nul 2>&1
if errorlevel 1 (
    echo [ERROR] You are not logged in to Fly.io. Please run:
    echo   fly auth login
    exit /b 1
)
echo [SUCCESS] Fly.io authentication verified

REM Prompt for secrets
echo [INFO] We need to collect your secrets for deployment...

if "%SUPABASE_URL%"=="" (
    set /p SUPABASE_URL="Enter your Supabase URL (https://your-project.supabase.co): "
)

if "%SUPABASE_SERVICE_KEY%"=="" (
    set /p SUPABASE_SERVICE_KEY="Enter your Supabase Service Key: "
)

if "%OPENAI_API_KEY%"=="" (
    set /p OPENAI_API_KEY="Enter your OpenAI API Key: "
)

set /p LOGFIRE_TOKEN="Enter your Logfire token (optional, press Enter to skip): "

echo [INFO] Deploying services in dependency order...

REM Deploy Archon Server
echo [INFO] Deploying Archon Server...
fly apps list | findstr "archon-server" >nul
if errorlevel 1 (
    echo [INFO] Creating new app: archon-server
    fly apps create archon-server
) else (
    echo [INFO] App archon-server already exists
)

echo [INFO] Deploying Archon Server with config fly.toml...
fly deploy -c fly.toml -a archon-server
if errorlevel 1 (
    echo [ERROR] Failed to deploy Archon Server
    exit /b 1
)

echo [INFO] Setting secrets for archon-server...
fly secrets set SUPABASE_URL="%SUPABASE_URL%" -a archon-server
fly secrets set SUPABASE_SERVICE_KEY="%SUPABASE_SERVICE_KEY%" -a archon-server
fly secrets set OPENAI_API_KEY="%OPENAI_API_KEY%" -a archon-server
if not "%LOGFIRE_TOKEN%"=="" (
    fly secrets set LOGFIRE_TOKEN="%LOGFIRE_TOKEN%" -a archon-server
)
echo [SUCCESS] Archon Server deployed successfully!

REM Deploy Agents Service
echo [INFO] Deploying Agents Service...
fly apps list | findstr "archon-agents" >nul
if errorlevel 1 (
    echo [INFO] Creating new app: archon-agents
    fly apps create archon-agents
) else (
    echo [INFO] App archon-agents already exists
)

echo [INFO] Deploying Agents Service with config fly-agents.toml...
fly deploy -c fly-agents.toml -a archon-agents
if errorlevel 1 (
    echo [ERROR] Failed to deploy Agents Service
    exit /b 1
)

echo [INFO] Setting secrets for archon-agents...
fly secrets set SUPABASE_URL="%SUPABASE_URL%" -a archon-agents
fly secrets set SUPABASE_SERVICE_KEY="%SUPABASE_SERVICE_KEY%" -a archon-agents
fly secrets set OPENAI_API_KEY="%OPENAI_API_KEY%" -a archon-agents
if not "%LOGFIRE_TOKEN%"=="" (
    fly secrets set LOGFIRE_TOKEN="%LOGFIRE_TOKEN%" -a archon-agents
)
echo [SUCCESS] Agents Service deployed successfully!

REM Deploy MCP Server
echo [INFO] Deploying MCP Server...
fly apps list | findstr "archon-mcp" >nul
if errorlevel 1 (
    echo [INFO] Creating new app: archon-mcp
    fly apps create archon-mcp
) else (
    echo [INFO] App archon-mcp already exists
)

echo [INFO] Deploying MCP Server with config fly-mcp.toml...
fly deploy -c fly-mcp.toml -a archon-mcp
if errorlevel 1 (
    echo [ERROR] Failed to deploy MCP Server
    exit /b 1
)

echo [INFO] Setting secrets for archon-mcp...
fly secrets set SUPABASE_URL="%SUPABASE_URL%" -a archon-mcp
fly secrets set SUPABASE_SERVICE_KEY="%SUPABASE_SERVICE_KEY%" -a archon-mcp
if not "%LOGFIRE_TOKEN%"=="" (
    fly secrets set LOGFIRE_TOKEN="%LOGFIRE_TOKEN%" -a archon-mcp
)
echo [SUCCESS] MCP Server deployed successfully!

REM Deploy Frontend
echo [INFO] Deploying Frontend...
fly apps list | findstr "archon-frontend" >nul
if errorlevel 1 (
    echo [INFO] Creating new app: archon-frontend
    fly apps create archon-frontend
) else (
    echo [INFO] App archon-frontend already exists
)

echo [INFO] Deploying Frontend with config fly-frontend.toml...
fly deploy -c fly-frontend.toml -a archon-frontend
if errorlevel 1 (
    echo [ERROR] Failed to deploy Frontend
    exit /b 1
)
echo [SUCCESS] Frontend deployed successfully!

echo [SUCCESS] All services deployed!
echo.
echo [INFO] Your Archon services are now available at:
echo   Frontend:    https://archon-frontend.fly.dev
echo   API Server:  https://archon-server.fly.dev
echo   MCP Server:  https://archon-mcp.fly.dev
echo   Agents:      https://archon-agents.fly.dev
echo.
echo [INFO] Next steps:
echo   1. Open the frontend URL and configure your API keys in Settings
echo   2. Test by uploading a document or crawling a website
echo   3. Connect your AI coding assistant to the MCP server
echo   4. Check the deployment guide (FLY_DEPLOYMENT.md) for more details

pause
