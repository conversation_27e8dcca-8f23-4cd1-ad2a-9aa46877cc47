#!/bin/bash

# Archon Fly.io Deployment Script
# This script helps deploy all Archon services to Fly.io in the correct order

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if flyctl is installed
check_flyctl() {
    if ! command -v fly &> /dev/null; then
        print_error "Fly.io CLI (flyctl) is not installed. Please install it first:"
        echo "  https://fly.io/docs/hands-on/install-flyctl/"
        exit 1
    fi
    print_success "Fly.io CLI found"
}

# Function to check if user is logged in to Fly.io
check_fly_auth() {
    if ! fly auth whoami &> /dev/null; then
        print_error "You are not logged in to Fly.io. Please run:"
        echo "  fly auth login"
        exit 1
    fi
    print_success "Fly.io authentication verified"
}

# Function to prompt for required environment variables
prompt_for_secrets() {
    print_status "We need to collect your secrets for deployment..."
    
    if [ -z "$SUPABASE_URL" ]; then
        read -p "Enter your Supabase URL (https://your-project.supabase.co): " SUPABASE_URL
    fi
    
    if [ -z "$SUPABASE_SERVICE_KEY" ]; then
        read -s -p "Enter your Supabase Service Key: " SUPABASE_SERVICE_KEY
        echo
    fi
    
    if [ -z "$OPENAI_API_KEY" ]; then
        read -s -p "Enter your OpenAI API Key: " OPENAI_API_KEY
        echo
    fi
    
    # Optional Logfire token
    read -p "Enter your Logfire token (optional, press Enter to skip): " LOGFIRE_TOKEN
}

# Function to deploy a service
deploy_service() {
    local service_name=$1
    local config_file=$2
    local app_name=$3
    
    print_status "Deploying $service_name..."
    
    # Create app if it doesn't exist
    if ! fly apps list | grep -q "$app_name"; then
        print_status "Creating new app: $app_name"
        fly apps create "$app_name"
    else
        print_status "App $app_name already exists"
    fi
    
    # Deploy the service
    print_status "Deploying $service_name with config $config_file..."
    fly deploy -c "$config_file" -a "$app_name"
    
    # Set secrets
    print_status "Setting secrets for $app_name..."
    fly secrets set SUPABASE_URL="$SUPABASE_URL" -a "$app_name"
    fly secrets set SUPABASE_SERVICE_KEY="$SUPABASE_SERVICE_KEY" -a "$app_name"
    
    if [ "$service_name" != "MCP Server" ]; then
        fly secrets set OPENAI_API_KEY="$OPENAI_API_KEY" -a "$app_name"
    fi
    
    if [ -n "$LOGFIRE_TOKEN" ]; then
        fly secrets set LOGFIRE_TOKEN="$LOGFIRE_TOKEN" -a "$app_name"
    fi
    
    print_success "$service_name deployed successfully!"
}

# Function to update service URLs in config files
update_service_urls() {
    print_status "Updating service URLs in configuration files..."
    
    # Update MCP server config with actual service URLs
    sed -i.bak "s|API_SERVICE_URL = \"https://archon-server.fly.dev\"|API_SERVICE_URL = \"https://archon-server.fly.dev\"|g" fly-mcp.toml
    sed -i.bak "s|AGENTS_SERVICE_URL = \"https://archon-agents.fly.dev\"|AGENTS_SERVICE_URL = \"https://archon-agents.fly.dev\"|g" fly-mcp.toml
    
    # Update frontend config with server URL
    sed -i.bak "s|VITE_API_URL = \"https://archon-server.fly.dev\"|VITE_API_URL = \"https://archon-server.fly.dev\"|g" fly-frontend.toml
    
    print_success "Service URLs updated"
}

# Function to check service health
check_service_health() {
    local service_url=$1
    local service_name=$2
    local health_path=$3
    
    print_status "Checking health of $service_name..."
    
    # Wait a bit for service to start
    sleep 10
    
    if curl -f -s "$service_url$health_path" > /dev/null; then
        print_success "$service_name is healthy"
        return 0
    else
        print_warning "$service_name health check failed - this might be normal during startup"
        return 1
    fi
}

# Main deployment function
main() {
    print_status "Starting Archon deployment to Fly.io..."
    
    # Pre-flight checks
    check_flyctl
    check_fly_auth
    
    # Collect secrets
    prompt_for_secrets
    
    # Update configuration files
    update_service_urls
    
    print_status "Deploying services in dependency order..."
    
    # Deploy services in order
    deploy_service "Archon Server" "fly.toml" "archon-server"
    deploy_service "Agents Service" "fly-agents.toml" "archon-agents"
    deploy_service "MCP Server" "fly-mcp.toml" "archon-mcp"
    deploy_service "Frontend" "fly-frontend.toml" "archon-frontend"
    
    print_success "All services deployed!"
    
    # Health checks
    print_status "Performing health checks..."
    check_service_health "https://archon-server.fly.dev" "Archon Server" "/health"
    check_service_health "https://archon-agents.fly.dev" "Agents Service" "/health"
    check_service_health "https://archon-mcp.fly.dev" "MCP Server" "/sse"
    check_service_health "https://archon-frontend.fly.dev" "Frontend" "/"
    
    print_success "Deployment complete!"
    echo
    print_status "Your Archon services are now available at:"
    echo "  Frontend:    https://archon-frontend.fly.dev"
    echo "  API Server:  https://archon-server.fly.dev"
    echo "  MCP Server:  https://archon-mcp.fly.dev"
    echo "  Agents:      https://archon-agents.fly.dev"
    echo
    print_status "Next steps:"
    echo "  1. Open the frontend URL and configure your API keys in Settings"
    echo "  2. Test by uploading a document or crawling a website"
    echo "  3. Connect your AI coding assistant to the MCP server"
    echo "  4. Check the deployment guide (FLY_DEPLOYMENT.md) for more details"
}

# Run main function
main "$@"
