# Archon Fly.io Deployment Guide

This guide documents the complete process of deploying the Archon system to Fly.io, including the implementation of SSE (Server-Sent Events) transport for the MCP server.

## 🏗️ Architecture Overview

The Archon system consists of 4 microservices deployed on Fly.io:

- **archon-server** (`https://archon-server.fly.dev`) - Main API server
- **archon-frontend** (`https://archon-frontend.fly.dev`) - React frontend with Vite
- **archon-agents** (`https://archon-agents.fly.dev`) - AI agents service
- **archon-mcp** (`https://archon-mcp.fly.dev`) - MCP server with SSE transport

## 📋 Prerequisites

1. **Fly.io CLI** installed and authenticated
2. **Docker** (for local testing)
3. **Node.js 18+** (for frontend development)
4. **Python 3.11+** (for backend services)

## 🚀 Deployment Process

### Step 1: Initialize Fly.io Applications

```bash
# Create applications (one-time setup)
fly apps create archon-server
fly apps create archon-frontend  
fly apps create archon-agents
fly apps create archon-mcp
```

### Step 2: Configure Environment Variables

Set up secrets for each application:

```bash
# Server secrets
fly secrets set -a archon-server \
  OPENAI_API_KEY="your-openai-key" \
  ANTHROPIC_API_KEY="your-anthropic-key" \
  MCP_SERVICE_URL="https://archon-mcp.fly.dev"

# Agents secrets
fly secrets set -a archon-agents \
  OPENAI_API_KEY="your-openai-key" \
  ANTHROPIC_API_KEY="your-anthropic-key"

# MCP secrets
fly secrets set -a archon-mcp \
  OPENAI_API_KEY="your-openai-key" \
  ANTHROPIC_API_KEY="your-anthropic-key"
```

### Step 3: Deploy Services

Deploy in the following order to handle dependencies:

```bash
# 1. Deploy MCP service first
fly deploy -c fly-mcp.toml -a archon-mcp

# 2. Deploy agents service
fly deploy -c fly-agents.toml -a archon-agents

# 3. Deploy main server
fly deploy -a archon-server

# 4. Deploy frontend last
fly deploy -c fly-frontend.toml -a archon-frontend
```

### Step 4: Verify Deployment

Check that all services are running:

```bash
fly status -a archon-server
fly status -a archon-frontend
fly status -a archon-agents
fly status -a archon-mcp
```

Test endpoints:
- Server: `https://archon-server.fly.dev/api/health`
- Frontend: `https://archon-frontend.fly.dev`
- Agents: `https://archon-agents.fly.dev/health`
- MCP: `https://archon-mcp.fly.dev/sse` (SSE endpoint)

## 🔧 Key Configuration Files

### Fly.io Configuration Files

- `fly.toml` - Main server configuration
- `fly-frontend.toml` - Frontend configuration
- `fly-agents.toml` - Agents service configuration
- `fly-mcp.toml` - MCP service configuration

### Docker Files

- `Dockerfile.server.fly` - Multi-stage server build
- `Dockerfile.frontend.fly` - Frontend build with Node.js
- `Dockerfile.agents.fly` - Agents service build
- `Dockerfile.mcp.fly` - MCP service build

## 🌐 SSE Transport Implementation

### Overview

The MCP (Model Context Protocol) server was implemented using SSE (Server-Sent Events) transport instead of the default streamable HTTP transport. This provides better real-time communication and native browser support.

### Changes Made for SSE Transport

#### 1. MCP Server Transport Configuration

**File**: `python/src/mcp_server/mcp_server.py`

```python
# Before (Streamable HTTP)
logger.info("   Mode: Streamable HTTP")
logger.info(f"   URL: http://{server_host}:{server_port}/mcp")
mcp.run(transport="streamable-http")

# After (SSE)
logger.info("   Mode: SSE (Server-Sent Events)")
logger.info(f"   URL: http://{server_host}:{server_port}/sse")
mcp.run(transport="sse")
```

#### 2. Frontend Proxy Configuration

**File**: `archon-ui-main/vite.config.ts`

```typescript
// Before
'/mcp': {
  target: mcpProxyTarget,
  // ... proxy config
}

// After  
'/sse': {
  target: mcpProxyTarget,
  changeOrigin: true,
  secure: isFlyDeployment,
  ws: true, // Support WebSocket connections for SSE
  // ... proxy config
}
```

#### 3. Frontend MCP Client Service

**File**: `archon-ui-main/src/services/mcpClientService.ts`

```typescript
// Before
const mcpUrl = `${window.location.origin}/mcp`;
const archonConfig: MCPClientConfig = {
  transport_type: 'http',
  // ...
};

// After
const mcpUrl = `${window.location.origin}/sse`;
const archonConfig: MCPClientConfig = {
  transport_type: 'sse',
  // ...
};
```

#### 4. Backend Health Check

**File**: `python/src/server/api_routes/mcp_api.py`

```python
# Before
response = client.get(f"{self.mcp_service_url}/mcp")

# After
response = client.get(f"{self.mcp_service_url}/sse")
```

### SSE vs Streamable HTTP Comparison

| Feature | Streamable HTTP | SSE |
|---------|----------------|-----|
| **Protocol** | HTTP with streaming | Server-Sent Events |
| **Endpoint** | `/mcp` | `/sse` |
| **Browser Support** | Custom implementation | Native EventSource API |
| **Connection Type** | HTTP requests | Persistent event streams |
| **Real-time Updates** | ✅ | ✅ |
| **Reconnection** | Manual | Automatic |

### Benefits of SSE Transport

1. **Native Browser Support**: Uses the standard EventSource API
2. **Automatic Reconnection**: Built-in reconnection handling
3. **Better Error Handling**: Standard SSE error events
4. **Simpler Client Code**: Less custom streaming logic needed
5. **HTTP/2 Compatibility**: Works well with modern HTTP protocols

## 🔍 Service Discovery Implementation

### Dynamic Environment Detection

The system automatically detects the deployment environment and configures service URLs accordingly:

**File**: `python/src/server/config/service_discovery.py`

```python
def get_service_urls():
    """Get service URLs based on environment detection."""
    if is_fly_deployment():
        return {
            'server_url': 'https://archon-server.fly.dev',
            'agents_url': 'https://archon-agents.fly.dev', 
            'mcp_url': 'https://archon-mcp.fly.dev'
        }
    else:
        return {
            'server_url': 'http://localhost:8000',
            'agents_url': 'http://localhost:8001',
            'mcp_url': 'http://localhost:8051'
        }
```

### Fly.io Environment Detection

```python
def is_fly_deployment():
    """Detect if running in Fly.io environment."""
    return (
        os.getenv('FLY_APP_NAME') is not None or
        os.getenv('FLY_ALLOC_ID') is not None or
        'fly.dev' in os.getenv('HOSTNAME', '')
    )
```

## 🏥 Health Checks and Monitoring

### Health Check Endpoints

- **Server**: `/api/health` - Overall system health
- **Agents**: `/health` - Agents service health  
- **MCP**: `/sse` - SSE endpoint health (returns 406 for HTTP requests)
- **Frontend**: Served by Nginx, no specific health endpoint

### MCP Health Check Logic

The MCP service health check expects a 406 "Not Acceptable" response for regular HTTP requests, which indicates the SSE endpoint is working correctly:

```python
def _get_fly_service_status(self) -> str:
    try:
        response = client.get(f"{self.mcp_service_url}/sse")
        # 406 = SSE endpoint rejecting HTTP request (correct behavior)
        if response.status_code == 406:
            return "running"
        elif response.status_code == 200:
            return "running"
        else:
            return "unhealthy"
    except Exception:
        return "error"
```

## 💰 Cost Estimation

### Current Resource Allocation

- **archon-server**: `shared-cpu-2x:2048MB` (~$3.88/month)
- **archon-frontend**: `shared-cpu-1x:1024MB` (~$1.94/month)
- **archon-agents**: `shared-cpu-2x:4096MB` (~$5.82/month)
- **archon-mcp**: `shared-cpu-1x:1024MB` (~$1.94/month)

**Total Estimated Cost**: ~$13.58/month (before free tier credits)

### Cost Optimization Tips

1. **Auto-stop machines** when idle
2. **Scale down** resource allocation if not needed
3. **Remove unused machines** 
4. **Use free tier credits** (~$5/month for personal accounts)

## 🔧 Troubleshooting

### Common Issues

1. **Service Discovery Failures**
   - Check environment variables are set correctly
   - Verify Fly.io environment detection logic

2. **MCP Connection Issues**
   - Ensure SSE endpoint is accessible
   - Check proxy configuration in frontend
   - Verify transport type matches in client

3. **Deployment Failures**
   - Check Docker build context
   - Verify all required files are included
   - Review Fly.io logs: `fly logs -a <app-name>`

### Debugging Commands

```bash
# Check application status
fly status -a <app-name>

# View logs
fly logs -a <app-name>

# SSH into machine
fly ssh console -a <app-name>

# Check machine resources
fly machine list -a <app-name>
```

## 🎯 Next Steps

1. **Set up monitoring** with Fly.io metrics
2. **Configure custom domains** if needed
3. **Implement CI/CD pipeline** for automated deployments
4. **Add SSL certificates** for custom domains
5. **Set up backup strategies** for persistent data

## 🔄 Development Workflow

### Local Development

1. **Start services locally**:
```bash
# Terminal 1 - Server
cd python && python -m src.server.main

# Terminal 2 - Agents
cd python && python -m src.agents.main

# Terminal 3 - MCP Server
cd python && python -m src.mcp_server.mcp_server

# Terminal 4 - Frontend
cd archon-ui-main && npm run dev
```

2. **Test SSE connection locally**:
```bash
curl -N -H "Accept: text/event-stream" http://localhost:8051/sse
```

### Deployment Workflow

1. **Test changes locally** first
2. **Deploy MCP service** (if MCP changes made)
3. **Deploy backend services** (server, agents)
4. **Deploy frontend** last
5. **Verify all endpoints** are working
6. **Check logs** for any errors

### Environment-Specific Configuration

The system automatically adapts to different environments:

- **Local Development**: Uses localhost URLs
- **Fly.io Production**: Uses fly.dev URLs
- **Service Discovery**: Automatic environment detection

## 🛡️ Security Considerations

### API Keys Management

- Store sensitive keys in Fly.io secrets
- Never commit API keys to version control
- Use environment-specific configurations
- Rotate keys regularly

### Network Security

- All services use HTTPS in production
- Internal service communication via Fly.io private network
- Frontend proxy prevents direct MCP access
- CORS configured for allowed origins

### Access Control

- MCP server only accessible via frontend proxy
- Health endpoints publicly accessible for monitoring
- Admin endpoints protected (if implemented)

## 📊 Monitoring and Observability

### Built-in Monitoring

- **Fly.io Metrics**: CPU, memory, network usage
- **Health Checks**: Automated service health monitoring
- **Logs**: Centralized logging via `fly logs`
- **Alerts**: Can be configured via Fly.io dashboard

### Custom Monitoring

The system includes custom health check endpoints:

```python
# Server health check
GET /api/health
{
  "status": "healthy",
  "services": {
    "mcp": "running",
    "agents": "running"
  }
}

# MCP status check
GET /api/mcp/status
{
  "status": "running",
  "uptime": "2h 15m",
  "container_status": "running"
}
```

### Log Analysis

Key log patterns to monitor:

- **SSE Connections**: `♻️ Reusing existing context for new SSE connection`
- **MCP Requests**: `Processing request of type ListToolsRequest`
- **Health Checks**: `API service health check`
- **Errors**: Look for `ERROR` or `WARNING` level logs

## 🔧 Advanced Configuration

### Custom Domains

To use custom domains:

1. **Add certificate**:
```bash
fly certs create your-domain.com -a archon-frontend
```

2. **Update DNS**:
```
CNAME your-domain.com archon-frontend.fly.dev
```

3. **Update service discovery** to use custom domains

### Scaling Configuration

Adjust resources based on usage:

```bash
# Scale up server resources
fly scale memory 4096 -a archon-server
fly scale count 2 -a archon-server

# Scale down for cost optimization
fly scale memory 1024 -a archon-mcp
```

### Auto-scaling

Configure auto-scaling based on metrics:

```toml
# In fly.toml
[http_service.concurrency]
  type = "requests"
  hard_limit = 1000
  soft_limit = 800

[[services.ports]]
  handlers = ["http"]
  port = 80
  force_https = true
```

## 🚨 Disaster Recovery

### Backup Strategy

1. **Database Backups**: If using persistent storage
2. **Configuration Backups**: Store fly.toml files in version control
3. **Secrets Backup**: Document secret keys (store securely)
4. **Code Backups**: Use Git for version control

### Recovery Procedures

1. **Service Outage**:
   - Check Fly.io status page
   - Review application logs
   - Restart machines if needed: `fly machine restart <machine-id>`

2. **Complete Rebuild**:
   - Redeploy from Git repository
   - Restore secrets from secure storage
   - Verify all services are communicating

3. **Rollback Strategy**:
   - Use Git to revert to previous working version
   - Redeploy previous version
   - Monitor for stability

## 📈 Performance Optimization

### Frontend Optimization

- **Build Optimization**: Vite handles code splitting and minification
- **CDN**: Fly.io provides global edge caching
- **Compression**: Gzip/Brotli enabled by default

### Backend Optimization

- **Connection Pooling**: Implemented in HTTP clients
- **Caching**: Add Redis if needed for session storage
- **Database Optimization**: Use connection pooling for databases

### MCP Optimization

- **Connection Reuse**: SSE connections are reused efficiently
- **Session Management**: Proper session cleanup implemented
- **Resource Limits**: Configure appropriate memory limits

## 📚 Additional Resources

- [Fly.io Documentation](https://fly.io/docs/)
- [MCP Protocol Specification](https://modelcontextprotocol.io/)
- [Server-Sent Events MDN Guide](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)
- [Vite Proxy Configuration](https://vitejs.dev/config/server-options.html#server-proxy)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React + TypeScript Guide](https://react-typescript-cheatsheet.netlify.app/)
