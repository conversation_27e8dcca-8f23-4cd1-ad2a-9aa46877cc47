# Server Service - Web crawling and document processing microservice
# Fly.io version - builds from root context

# Build stage
FROM python:3.11 AS builder

WORKDIR /build

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY python/requirements.server.txt .
RUN pip install --user --no-cache-dir -r requirements.server.txt

# Runtime stage
FROM python:3.11-slim

WORKDIR /app

# Install runtime dependencies for Playwright (minimal set)
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xdg-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy Python packages from builder
COPY --from=builder /root/.local /root/.local

# Install Playwright browsers
ENV PATH=/root/.local/bin:$PATH
RUN playwright install chromium

# Copy server code and tests from python directory
COPY python/src/server/ src/server/
COPY python/src/__init__.py src/
COPY python/tests/ tests/

# Set environment variables
ENV PYTHONPATH="/app:/root/.local/lib/python3.11/site-packages:$PYTHONPATH"
ENV PYTHONUNBUFFERED=1

# Expose Server port
ARG ARCHON_SERVER_PORT=8181
ENV ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT}
EXPOSE ${ARCHON_SERVER_PORT}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD sh -c "python -c \"import urllib.request; urllib.request.urlopen('http://localhost:${ARCHON_SERVER_PORT}/health')\""

# Run the Server service
CMD sh -c "python -m uvicorn src.server.main:socket_app --host 0.0.0.0 --port ${ARCHON_SERVER_PORT} --workers 1"
