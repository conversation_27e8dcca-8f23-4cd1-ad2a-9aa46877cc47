# Archon Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================
# Supabase Configuration - Get these from your Supabase project dashboard
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.your-service-key-here

# =============================================================================
# AI/ML CONFIGURATION (REQUIRED)
# =============================================================================
# OpenAI API Key - Required for embeddings and AI operations
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional: Google Gemini API Key (alternative to OpenAI)
# GOOGLE_API_KEY=your-google-api-key-here

# Optional: Ollama Configuration (for local models)
# OLLAMA_BASE_URL=http://localhost:11434

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================
# Service Discovery Mode
# Options: local, docker_compose, fly_io
SERVICE_DISCOVERY_MODE=fly_io

# Logging Configuration
LOG_LEVEL=INFO

# Optional: Logfire Integration (for advanced logging)
LOGFIRE_ENABLED=false
LOGFIRE_TOKEN=your-logfire-token-here

# =============================================================================
# FLY.IO SPECIFIC CONFIGURATION
# =============================================================================
# Fly.io Region (change to your preferred region)
FLY_REGION=iad

# Service URLs (update these with your actual Fly.io app URLs after deployment)
API_SERVICE_URL=https://archon-server.fly.dev
MCP_SERVICE_URL=https://archon-mcp.fly.dev
AGENTS_SERVICE_URL=https://archon-agents.fly.dev
FRONTEND_SERVICE_URL=https://archon-frontend.fly.dev

# =============================================================================
# PORT CONFIGURATION (for local development)
# =============================================================================
# These are used for local development - Fly.io uses its own port mapping
ARCHON_UI_PORT=3737
ARCHON_SERVER_PORT=8181
ARCHON_MCP_PORT=8051
ARCHON_AGENTS_PORT=8052
ARCHON_DOCS_PORT=3838

# Host Configuration (for local development)
HOST=localhost

# =============================================================================
# OPTIONAL FEATURES
# =============================================================================
# Enable reranking RAG strategy (requires additional dependencies)
# Uncomment lines 20-22 in python/requirements.server.txt if enabling this
ENABLE_RERANKING=false

# MCP Transport Configuration
# Options: sse, stdio, websocket
TRANSPORT=sse

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Optional: JWT Secret for session management
# JWT_SECRET=your-jwt-secret-here

# Optional: CORS Origins (comma-separated list)
# CORS_ORIGINS=https://your-domain.com,https://another-domain.com

# =============================================================================
# DEPLOYMENT NOTES
# =============================================================================
# For Fly.io deployment:
# 1. Don't commit this file with real values
# 2. Use `fly secrets set` to set sensitive values:
#    fly secrets set SUPABASE_URL="https://your-project.supabase.co"
#    fly secrets set SUPABASE_SERVICE_KEY="your-service-key"
#    fly secrets set OPENAI_API_KEY="sk-your-openai-key"
# 3. Update service URLs after deploying each service
# 4. Make sure your Supabase database is accessible from Fly.io

# =============================================================================
# DEVELOPMENT VS PRODUCTION
# =============================================================================
# For local development, use docker-compose with these environment variables
# For Fly.io production, use fly secrets and update service URLs accordingly
