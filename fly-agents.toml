# Fly.io configuration for Archon Agents Service
# This deploys the AI/ML operations and reranking service

app = "archon-agents"
primary_region = "iad"  # Change to your preferred region
kill_signal = "SIGINT"
kill_timeout = "5s"

[experimental]
  auto_rollback = true

[build]
  dockerfile = "Dockerfile.agents.fly"

[env]
  # Service Configuration
  SERVICE_DISCOVERY_MODE = "fly_io"
  LOG_LEVEL = "INFO"
  PYTHONUNBUFFERED = "1"

  # Port Configuration
  ARCHON_AGENTS_PORT = "8052"
  ARCHON_SERVER_PORT = "8181"

  # Fly.io specific
  FLY_REGION = "iad"

[http_service]
  internal_port = 8052
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 500
    soft_limit = 400

[machine]
  memory = "4gb"
  cpu_kind = "shared"
  cpus = 2

[[services]]
  protocol = "tcp"
  internal_port = 8052
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["http", "tls"]

  [services.concurrency]
    type = "connections"
    hard_limit = 500
    soft_limit = 400

[[vm]]
  memory = "4gb"
  cpu_kind = "shared"
  cpus = 2

# Process configuration
[processes]
  app = "python -m uvicorn src.agents.server:app --host 0.0.0.0 --port 8052 --workers 1"

# Deploy configuration
[deploy]
  release_command = "echo 'Archon Agents Service starting...'"
  strategy = "rolling"

# Required secrets (set using `fly secrets set`):
# - SUPABASE_URL
# - SUPABASE_SERVICE_KEY
# - OPENAI_API_KEY
# - LOGFIRE_TOKEN (optional)
