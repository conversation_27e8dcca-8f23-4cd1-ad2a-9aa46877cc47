# Archon Fly.io Quick Start Guide

## 🚀 One-Command Deployment

For the fastest deployment, use our automated script:

### Windows
```cmd
deploy-to-fly.bat
```

### Linux/macOS
```bash
./deploy-to-fly.sh
```

## 📋 Prerequisites Checklist

- [ ] [Fly.io CLI installed](https://fly.io/docs/hands-on/install-flyctl/)
- [ ] Fly.io account created and authenticated (`fly auth login`)
- [ ] [Supabase project](https://supabase.com/) set up with database
- [ ] [OpenAI API key](https://platform.openai.com/api-keys) ready
- [ ] Database initialized with `migration/complete_setup.sql`

## 🔧 Manual Deployment (Step by Step)

If you prefer manual control:

### 1. Deploy Core Services

```bash
# Server (Core API)
fly apps create archon-server
fly deploy -c fly.toml -a archon-server
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-server
fly secrets set SUPABASE_SERVICE_KEY="your-key" -a archon-server
fly secrets set OPENAI_API_KEY="sk-your-key" -a archon-server

# Agents (AI Operations)
fly apps create archon-agents
fly deploy -c fly-agents.toml -a archon-agents
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-agents
fly secrets set SUPABASE_SERVICE_KEY="your-key" -a archon-agents
fly secrets set OPENAI_API_KEY="sk-your-key" -a archon-agents

# MCP Server (AI Client Interface)
fly apps create archon-mcp
fly deploy -c fly-mcp.toml -a archon-mcp
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a archon-mcp
fly secrets set SUPABASE_SERVICE_KEY="your-key" -a archon-mcp

# Frontend (Web UI)
fly apps create archon-frontend
fly deploy -c fly-frontend.toml -a archon-frontend
```

### 2. Verify Deployment

```bash
# Check all apps are running
fly status -a archon-server
fly status -a archon-agents
fly status -a archon-mcp
fly status -a archon-frontend

# Test health endpoints
curl https://archon-server.fly.dev/health
curl https://archon-agents.fly.dev/health
```

## 🌐 Your Deployed URLs

After deployment, your services will be available at:

- **Frontend**: https://archon-frontend.fly.dev
- **API Server**: https://archon-server.fly.dev
- **MCP Server**: https://archon-mcp.fly.dev (for AI clients)
- **Agents**: https://archon-agents.fly.dev

## ⚙️ Post-Deployment Setup

1. **Configure API Keys**:
   - Open https://archon-frontend.fly.dev
   - Go to Settings → Select your AI provider
   - Enter your API key (stored securely in Supabase)

2. **Test the System**:
   - Try crawling a documentation website
   - Upload a PDF document
   - Create a project and add tasks

3. **Connect AI Clients**:
   - Use MCP server URL: `https://archon-mcp.fly.dev`
   - Configure in Claude Code, Cursor, or other MCP clients

## 🔍 Monitoring & Logs

```bash
# View logs
fly logs -a archon-server
fly logs -a archon-mcp
fly logs -a archon-agents
fly logs -a archon-frontend

# Follow logs in real-time
fly logs -f -a archon-server

# Check resource usage
fly machine list -a archon-server
```

## 🛠️ Common Commands

```bash
# Restart a service
fly machine restart -a archon-server

# Scale a service
fly scale count 2 -a archon-server
fly scale memory 4gb -a archon-agents

# Update a service
fly deploy -c fly.toml -a archon-server

# SSH into a machine
fly ssh console -a archon-server

# View secrets (values hidden)
fly secrets list -a archon-server
```

## 🚨 Troubleshooting

### Service Won't Start
```bash
# Check logs for errors
fly logs -a archon-server

# Verify secrets are set
fly secrets list -a archon-server

# Check machine status
fly machine list -a archon-server
```

### Database Connection Issues
- Verify Supabase URL and service key
- Check if Supabase allows connections from Fly.io
- Ensure database was initialized with migration script

### Frontend Can't Reach API
- Check if `VITE_API_URL` in `fly-frontend.toml` points to correct server URL
- Verify CORS settings allow frontend domain

### MCP Connection Issues
- Ensure MCP server is accessible at `/sse` endpoint
- Check service URLs in `fly-mcp.toml` point to deployed services
- Verify AI client is configured with correct MCP server URL

## 📚 Next Steps

- Read the full [FLY_DEPLOYMENT.md](./FLY_DEPLOYMENT.md) guide
- Check [Archon documentation](https://github.com/coleam00/Archon) for usage
- Join [GitHub Discussions](https://github.com/coleam00/Archon/discussions) for support

## 💡 Tips

- Use `fly regions list` to choose the best region for your users
- Set up custom domains with `fly certs create your-domain.com`
- Create separate staging/production environments
- Monitor costs with `fly billing show`

Your Archon deployment should now be running smoothly on Fly.io! 🎉
