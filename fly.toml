# Fly.io configuration for Archon - Main Server Service
# This deploys the core Archon server (FastAPI + Socket.IO + crawling)
# Additional services (MCP, Agents, Frontend) can be deployed separately

app = "archon-server"
primary_region = "iad"  # Change to your preferred region
kill_signal = "SIGINT"
kill_timeout = "5s"

[experimental]
  auto_rollback = true

[build]
  dockerfile = "Dockerfile.server.fly"

[env]
  # Service Configuration
  SERVICE_DISCOVERY_MODE = "fly_io"
  LOG_LEVEL = "INFO"
  PYTHONUNBUFFERED = "1"
  
  # Port Configuration
  ARCHON_SERVER_PORT = "8181"
  ARCHON_MCP_PORT = "8051"
  ARCHON_AGENTS_PORT = "8052"

  # Service URLs
  MCP_SERVICE_URL = "https://archon-mcp.fly.dev"
  AGENTS_SERVICE_URL = "https://archon-agents.fly.dev"

  # Fly.io specific
  FLY_REGION = "iad"

[http_service]
  internal_port = 8181
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800

[machine]
  memory = "2gb"
  cpu_kind = "shared"
  cpus = 2

[[services]]
  protocol = "tcp"
  internal_port = 8181
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["http", "tls"]

  [services.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800



[[vm]]
  memory = "2gb"
  cpu_kind = "shared"
  cpus = 2

# Volume for persistent storage (optional)
# Uncomment if you need persistent file storage
# [[mounts]]
#   source = "archon_data"
#   destination = "/app/data"

# Process configuration
[processes]
  app = "python -m uvicorn src.server.main:socket_app --host 0.0.0.0 --port 8181 --workers 1"

# Secrets (set these using `fly secrets set`)
# Required secrets:
# - SUPABASE_URL
# - SUPABASE_SERVICE_KEY  
# - OPENAI_API_KEY
# 
# Optional secrets:
# - LOGFIRE_TOKEN (if using Logfire logging)
#
# Set secrets with:
# fly secrets set SUPABASE_URL="https://your-project.supabase.co"
# fly secrets set SUPABASE_SERVICE_KEY="your-service-key"
# fly secrets set OPENAI_API_KEY="sk-your-openai-key"

# Deploy configuration
[deploy]
  release_command = "echo 'Archon Server starting...'"
  strategy = "rolling"

# Metrics and monitoring
[metrics]
  port = 9091
  path = "/metrics"
