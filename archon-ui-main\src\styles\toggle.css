.toggle-switch {
  position: relative;
  width: 84px;
  height: 44px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 44px;
  padding: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}
.toggle-switch:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.5);
}
.toggle-thumb {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: transparent;
  border: 2px solid rgba(var(--accent-color-rgb), 0.5);
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}
.toggle-icon {
  color: rgba(var(--accent-color-rgb), 0.7);
  width: 20px;
  height: 20px;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  filter: drop-shadow(0 0 0 rgba(var(--accent-color-rgb), 0));
}
.toggle-checked .toggle-thumb {
  transform: translateX(40px);
}
.toggle-checked .toggle-icon {
  color: rgba(var(--accent-color-rgb), 1);
  filter: drop-shadow(0 0 5px rgba(var(--accent-color-rgb), 0.7));
}
/* Glow animations */
@keyframes toggleGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.3); }
}
/* Color variants */
.toggle-purple {
  --accent-color-rgb: 168, 85, 247;
}
.toggle-purple.toggle-checked {
  background: rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.5);
  box-shadow: 0 0 18px rgba(168, 85, 247, 0.5);
  animation: toggleGlow 2s ease-in-out infinite;
}
.toggle-purple .toggle-thumb {
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
}
.toggle-purple.toggle-checked .toggle-thumb {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.7);
}
.toggle-green {
  --accent-color-rgb: 16, 185, 129;
}
.toggle-green.toggle-checked {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
  box-shadow: 0 0 18px rgba(16, 185, 129, 0.5);
  animation: toggleGlow 2s ease-in-out infinite;
}
.toggle-green .toggle-thumb {
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}
.toggle-green.toggle-checked .toggle-thumb {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.7);
}
.toggle-pink {
  --accent-color-rgb: 236, 72, 153;
}
.toggle-pink.toggle-checked {
  background: rgba(236, 72, 153, 0.2);
  border-color: rgba(236, 72, 153, 0.5);
  box-shadow: 0 0 18px rgba(236, 72, 153, 0.5);
  animation: toggleGlow 2s ease-in-out infinite;
}
.toggle-pink .toggle-thumb {
  box-shadow: 0 0 10px rgba(236, 72, 153, 0.3);
}
.toggle-pink.toggle-checked .toggle-thumb {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.7);
}
.toggle-blue {
  --accent-color-rgb: 59, 130, 246;
}
.toggle-blue.toggle-checked {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 18px rgba(59, 130, 246, 0.5);
  animation: toggleGlow 2s ease-in-out infinite;
}
.toggle-blue .toggle-thumb {
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}
.toggle-blue.toggle-checked .toggle-thumb {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.7);
}
.toggle-orange {
  --accent-color-rgb: 249, 115, 22;
}
.toggle-orange.toggle-checked {
  background: rgba(249, 115, 22, 0.2);
  border-color: rgba(249, 115, 22, 0.5);
  box-shadow: 0 0 18px rgba(249, 115, 22, 0.5);
  animation: toggleGlow 2s ease-in-out infinite;
}
.toggle-orange .toggle-thumb {
  box-shadow: 0 0 10px rgba(249, 115, 22, 0.3);
}
.toggle-orange.toggle-checked .toggle-thumb {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.7);
}
/* Dark mode adjustments */
.dark .toggle-switch {
  background: rgba(255, 255, 255, 0.1);
}
/* Disabled state */
.toggle-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}