# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:

  # Check for updates to GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"

  # Check for updates to Python packages in root (actual iteration)
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"

  # Check for updates to Python packages in mcp
  - package-ecosystem: "pip"
    directory: "/mcp"
    schedule:
      interval: "weekly"

  # Check for updates in Dockerfile
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"

  # Check for updates in MCP Dockerfile
  - package-ecosystem: "docker"
    directory: "/mcp"
    schedule:
      interval: "weekly"


  # Aditional: Structure to maintain previous iterations

  # Update Version 1: Single Agent
  # - package-ecosystem: "pip"
  #   directory: "/iterations/v1-single-agent"
  #   schedule:
  #     interval: "monthly"

  # Update Version 2: Agentic Workflow
  # - package-ecosystem: "pip"
  #   directory: "/iterations/v2-agentic-workflow"
  #   schedule:
  #     interval: "monthly"

  # Upate Version 3: MCP Support
  # - package-ecosystem: "pip"
  #   directory: "/iterations/v3-mcp-support"
  #   schedule:
  #     interval: "monthly"
